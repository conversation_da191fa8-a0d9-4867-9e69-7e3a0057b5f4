import 'package:bop_maps/models/artist_with_genre.dart';
import 'package:bop_maps/services/music/spotify_service.dart';
import 'package:bop_maps/providers/user_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:async';

/// Artist preferences management tab widget
class ArtistPreferencesTab extends StatefulWidget {
  final List<String> initialArtists;
  final Function(List<String>) onArtistsChanged;
  final Function(List<ArtistWithGenre>)? onArtistObjectsChanged;

  const ArtistPreferencesTab({
    super.key,
    required this.initialArtists,
    required this.onArtistsChanged,
    this.onArtistObjectsChanged,
  });

  @override
  State<ArtistPreferencesTab> createState() => _ArtistPreferencesTabState();
}

class _ArtistPreferencesTabState extends State<ArtistPreferencesTab>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final SpotifyService _spotifyService = SpotifyService();
  final ScrollController _scrollController = ScrollController();
  Timer? _searchTimer;
  
  // State
  List<ArtistWithGenre> _selectedArtists = [];
  List<ArtistWithGenre> _searchResults = [];
  bool _isSearching = false;
  bool _isLoadingImages = false;

  // Keep alive to preserve tab state
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _loadInitialArtists();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }

  /// Load initial artists from cached user data with fallback to Spotify for missing images
  Future<void> _loadInitialArtists() async {
    if (widget.initialArtists.isEmpty) return;

    setState(() {
      _isLoadingImages = true;
    });

    try {
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final user = userProvider.currentUser;
      final List<ArtistWithGenre> artists = [];
      
      for (final artistName in widget.initialArtists) {
        try {
          // Try to get cached data from user preferences first
          String? imageUrl = user?.artistImageUrls?[artistName];
          String? spotifyId = user?.artistSpotifyIds?[artistName];
          List<String> genres = user?.artistGenres?[artistName] ?? [];
          
          // Only fallback to Spotify search if image is missing
          if (imageUrl == null || imageUrl.isEmpty) {
            try {
              final searchResults = await _spotifyService.searchArtists(artistName, limit: 1);
              if (searchResults.isNotEmpty) {
                final spotifyArtist = searchResults[0];
                imageUrl = spotifyArtist['images']?.isNotEmpty == true 
                    ? spotifyArtist['images'][0]['url'] 
                    : null;
                spotifyId ??= spotifyArtist['id'];
                if (genres.isEmpty && spotifyArtist['genres'] != null) {
                  genres = List<String>.from(spotifyArtist['genres']);
                }
              }
            } catch (e) {
              print('Fallback Spotify search failed for $artistName: $e');
            }
          }
          
          // Create artist with cached/fetched data
          final artist = ArtistWithGenre(
            id: spotifyId ?? artistName.toLowerCase().replaceAll(' ', '-'),
            name: artistName,
            imageUrl: imageUrl,
            genres: genres,
            popularity: 50, // Default popularity
            url: spotifyId != null ? 'https://open.spotify.com/artist/$spotifyId' : '',
            sourceGenre: 'user_preference',
          );
          artists.add(artist);
          
        } catch (e) {
          print('Error loading artist $artistName: $e');
          // Add basic artist object on error
          artists.add(ArtistWithGenre(
            id: artistName.toLowerCase().replaceAll(' ', '-'),
            name: artistName,
            imageUrl: null,
            genres: [],
            popularity: 50,
            url: '',
            sourceGenre: 'user_preference',
          ));
        }
      }

      if (mounted) {
        setState(() {
          _selectedArtists = artists;
          _isLoadingImages = false;
        });
      }
    } catch (e) {
      print('Error loading initial artists: $e');
      if (mounted) {
        setState(() {
          _isLoadingImages = false;
        });
      }
    }
  }

  /// Search for artists with debouncing
  void _searchArtists(String query) {
    print('🔍 [ArtistPreferencesTab] _searchArtists called with query: "$query"');
    _searchTimer?.cancel();
    
    if (query.trim().length < 1) {
      print('🔍 [ArtistPreferencesTab] Query too short, clearing results');
      setState(() {
        _searchResults = [];
      });
      return;
    }

    print('🔍 [ArtistPreferencesTab] Starting search timer for query: "$query"');
    _searchTimer = Timer(const Duration(milliseconds: 300), () {
      print('🔍 [ArtistPreferencesTab] Timer fired, calling _performSearch');
      _performSearch(query);
    });
  }

  /// Perform the actual search with cached data priority
  Future<void> _performSearch(String query) async {
    if (!mounted) return;

    setState(() {
      _isSearching = true;
    });

    try {
      print('🔍 [ArtistPreferencesTab] Starting search for query: "$query"');
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final user = userProvider.currentUser;
      print('🔍 [ArtistPreferencesTab] User provider found: true');
      print('🔍 [ArtistPreferencesTab] Current user: ${user != null}');
      print('🔍 [ArtistPreferencesTab] User top artists: ${user?.topArtists?.length ?? 0}');
      
      final List<ArtistWithGenre> artists = [];
      
      // First, check if any of the user's cached artists match the search query
      if (user?.topArtists != null) {
        print('🔍 [ArtistPreferencesTab] Searching cached artists...');
        final cachedMatches = user!.topArtists!
            .where((artistName) => artistName.toLowerCase().contains(query.toLowerCase()))
            .where((artistName) => !_selectedArtists.any((selected) => 
                selected.name.toLowerCase() == artistName.toLowerCase()))
            .take(3); // Limit cached results
            
        for (final artistName in cachedMatches) {
          final imageUrl = user.artistImageUrls?[artistName];
          final spotifyId = user.artistSpotifyIds?[artistName];
          final genres = user.artistGenres?[artistName] ?? [];
          
          artists.add(ArtistWithGenre(
            id: spotifyId ?? artistName.toLowerCase().replaceAll(' ', '-'),
            name: artistName,
            imageUrl: imageUrl,
            genres: genres,
            popularity: 75, // Higher priority for cached artists
            url: spotifyId != null ? 'https://open.spotify.com/artist/$spotifyId' : '',
            sourceGenre: 'cached_preference',
          ));
        }
      }
      
      // Check if we need to search Spotify
      final bool needsSpotifySearch = artists.length < 3 || 
          artists.any((artist) => artist.imageUrl == null || artist.imageUrl!.isEmpty);
      
      if (needsSpotifySearch) {
        print('🎵 [ArtistPreferencesTab] Cache insufficient (${artists.length} results, some missing images). Searching Spotify...');
        try {
          final spotifyResults = await _spotifyService.searchArtists(query, limit: 10 - artists.length);
          print('🎵 [ArtistPreferencesTab] Spotify returned ${spotifyResults.length} results');
          
          final spotifyArtists = spotifyResults.map((artist) => 
            ArtistWithGenre.fromSpotifyResponse(artist, 'search')
          ).toList();
          print('🎵 [ArtistPreferencesTab] Converted to ${spotifyArtists.length} ArtistWithGenre objects');
          
          // Filter out artists that are already selected or in cached results
          final filteredSpotifyArtists = spotifyArtists.where((artist) => 
            !_selectedArtists.any((selected) => selected.name.toLowerCase() == artist.name.toLowerCase()) &&
            !artists.any((cached) => cached.name.toLowerCase() == artist.name.toLowerCase())
          ).toList();
          print('🎵 [ArtistPreferencesTab] After filtering: ${filteredSpotifyArtists.length} artists');
          
          artists.addAll(filteredSpotifyArtists);
        } catch (spotifyError) {
          print('❌ [ArtistPreferencesTab] Spotify search failed: $spotifyError');
          // Continue with just cached results if Spotify fails
        }
      } else {
        print('✅ [ArtistPreferencesTab] Cache sufficient! Using ${artists.length} cached results, skipping Spotify.');
      }
      
      print('✅ [ArtistPreferencesTab] Final search results: ${artists.length} artists');
      for (final artist in artists) {
        print('   - ${artist.name} (${artist.sourceGenre}) - Image: ${artist.imageUrl != null}');
      }
      
      if (mounted) {
        setState(() {
          _searchResults = artists;
          _isSearching = false;
        });

        // Scroll to top when search results are received
        if (artists.isNotEmpty && _scrollController.hasClients) {
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      }
    } catch (e) {
      print('Error performing search: $e');
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
      }
    }
  }

  /// Toggle artist selection
  void _toggleArtist(ArtistWithGenre artist) {
    final isSelected = _selectedArtists.any((a) => a.name.toLowerCase() == artist.name.toLowerCase());
    
    setState(() {
      if (isSelected) {
        _selectedArtists.removeWhere((a) => a.name.toLowerCase() == artist.name.toLowerCase());
      } else {
        // Check if we're at the limit (50 artists max)
        if (_selectedArtists.length >= 50) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Maximum 50 artists allowed'),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }
        _selectedArtists.add(artist);
      }
    });

    // Update parent with artist names (this only updates local state, doesn't save)
    final artistNames = _selectedArtists.map((a) => a.name).toList();
    widget.onArtistsChanged(artistNames);
    
    // Also pass full artist objects with metadata if callback provided
    widget.onArtistObjectsChanged?.call(_selectedArtists);
    
    HapticFeedback.lightImpact();
  }

  /// Remove artist from selection
  void _removeArtist(ArtistWithGenre artist) {
    setState(() {
      _selectedArtists.removeWhere((a) => a.name.toLowerCase() == artist.name.toLowerCase());
    });

    // Update parent with artist names
    final artistNames = _selectedArtists.map((a) => a.name).toList();
    widget.onArtistsChanged(artistNames);
    
    // Also pass full artist objects with metadata if callback provided
    widget.onArtistObjectsChanged?.call(_selectedArtists);
    
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search field
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search for artists...',
              prefixIcon: _isSearching
                  ? const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchResults = [];
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: _searchArtists,
          ),

          const SizedBox(height: 16),

          // Combined scrollable content
          if (_isLoadingImages)
            const Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Loading your artists...'),
                  ],
                ),
              ),
            )
          else
            Expanded(
              child: CustomScrollView(
                controller: _scrollController,
                slivers: [
                  // Search results section (appears first when available)
                  if (_searchResults.isNotEmpty) ...[
                    SliverToBoxAdapter(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Search Results',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            height: 180,
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: _searchResults.length,
                              itemBuilder: (context, index) {
                                final artist = _searchResults[index];
                                final isSelected = _selectedArtists.any(
                                  (a) => a.name.toLowerCase() == artist.name.toLowerCase(),
                                );
                                return Container(
                                  width: 150,
                                  margin: const EdgeInsets.only(right: 12),
                                  child: _buildArtistCard(artist, theme, isSelected: isSelected),
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: 24),
                        ],
                      ),
                    ),
                  ],

                  // Selected artists section header
                  SliverToBoxAdapter(
                    child: Row(
                      children: [
                        Text(
                          'Your Artists',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${_selectedArtists.length}/50',
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SliverToBoxAdapter(child: SizedBox(height: 8)),

                  // Selected artists grid or empty state
                  if (_selectedArtists.isEmpty)
                    SliverFillRemaining(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.person_search,
                              size: 64,
                              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No artists selected yet',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Search and select artists to personalize your AI recommendations',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    )
                  else
                    SliverGrid(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.85,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final artist = _selectedArtists[index];
                          return _buildArtistCard(artist, theme, isSelected: true);
                        },
                        childCount: _selectedArtists.length,
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build artist card widget
  Widget _buildArtistCard(ArtistWithGenre artist, ThemeData theme, {required bool isSelected}) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? theme.colorScheme.primary : Colors.transparent,
          width: 2,
        ),
        color: theme.cardColor.withValues(alpha: 0.1),
      ),
      child: InkWell(
        onTap: () => _toggleArtist(artist),
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // Artist Image
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: artist.imageUrl != null
                    ? Image.network(
                        artist.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(theme),
                      )
                    : _buildPlaceholderImage(theme),
              ),
            ),
            
            // Gradient overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
              ),
            ),
            
            // Artist Info
            Positioned(
              bottom: 12,
              left: 12,
              right: 12,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    artist.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (artist.genres.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      artist.genres.take(2).join(', '),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            
            // Selection indicator
            if (isSelected)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            
            // Remove button (only for selected artists in the main grid)
            if (isSelected && _selectedArtists.contains(artist))
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.6),
                    shape: BoxShape.circle,
                  ),
                  child: InkWell(
                    onTap: () => _removeArtist(artist),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build placeholder image for artists without images
  Widget _buildPlaceholderImage(ThemeData theme) {
    return Container(
      color: theme.colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.person,
        size: 40,
        color: theme.colorScheme.primary.withValues(alpha: 0.5),
      ),
    );
  }
}
